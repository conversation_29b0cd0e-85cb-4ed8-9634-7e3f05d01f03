from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import relationship

from app.models import Base


class Permission(Base):
    __tablename__ = 'permission'
    id = Column(Integer, primary_key=True, autoincrement=True, comment='权限编号')
    # user: add user:delete
    name = Column(String(20), comment='权限名称')
    description = Column(String(100), comment='权限描述')
    roles=relationship("Role", secondary="role_permission", back_populates="permissions", lazy="joined")

    def __repr__(self):
        # 获取类名
        class_name = self.__class__.__name__
        # 获取所有列属性
        columns = self.__class__.__table__.columns.keys()
        # 构建字段表示
        attrs = [f"{col}={getattr(self, col, None)!r}" for col in columns]
        return f"<{class_name}({', '.join(attrs)})>"