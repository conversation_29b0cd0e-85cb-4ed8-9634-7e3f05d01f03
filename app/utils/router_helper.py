from typing import List, Any, Dict

from fastapi import APIRouter
from starlette.responses import JSONResponse


class RouterHelper:
    def __init__(self,router:APIRouter):
        self.router = router

    def register_routes(self,router_map:List[Dict[str,Any]]):
        for route in router_map:
            self.router.add_api_route(
                route["path"],
                route["endpoint"],
                methods=route["methods"],
                response_class=route.get("response_model",JSONResponse),
                summary=route.get("summary",""),
                description=route.get("description")
            )