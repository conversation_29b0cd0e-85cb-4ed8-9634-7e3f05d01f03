from typing import Any, Dict, Optional, List


class ResponseUtils:
    # 成功状态码
    SUCCESS_CODE = 1
    # 失败的状态码
    FAILED_CODE = 0
    # 身份认证失败状态码
    UNAUTHORIZED_CODE = 401
    # 参数验证失败
    VALIDATION_ERROR_CODE = 422
    # 服务器内部报错
    SERVER_ERROR_CODE = 500

    @staticmethod
    def success(message: str = "操作成功", data: Any = None) -> Dict[str, Any]:
        """
        成功结果的响应
        :param message: 描述信息
        :param data: 返回的值
        :return: 返回字段类型
        """
        resp = {
            "code": ResponseUtils.SUCCESS_CODE,
            "message": message
        }
        if data is not None:
            resp["data"] = data
        return resp

    @staticmethod
    def failed(message: str = "操作失败", data: Any = None) -> Dict[str, Any]:
        """
        失败的响应结果
        :param message: 描述信息
        :param data: 返回的值
        :return: 返回字典类型
        """
        resp = {
            "code": ResponseUtils.FAILED_CODE,
            "message": message
        }
        if data is not None:
            resp["data"] = data
        return resp

    @staticmethod
    def unauthorized(message: str = "未授权访问") -> Dict[str, Any]:
        """
        401身份验证失败返回结果
        :param message:
        :return:
        """
        return {
            "code": ResponseUtils.UNAUTHORIZED_CODE,
            "message": message,
            "error": True
        }

    @staticmethod
    def server_error(message: str = "服务器内部报错") -> Dict[str, Any]:
        """
        500报错提示服务器内部出问题了
        :param message:
        :return:
        """
        return {
            "code": ResponseUtils.SERVER_ERROR_CODE,
            "message": message
        }

    @staticmethod
    def validation_error(
            message: str = "参数校验失败",
            validation_errors: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """
        参数校验失败返回错误信息
        :param message: 错误信息
        :param validation_errors: 信息详情
        :return:
        """
        resp = {
            "code": ResponseUtils.VALIDATION_ERROR_CODE,
            "message": message
        }
        if validation_errors is not None:
            resp["validation_errors"] = validation_errors
        return resp
