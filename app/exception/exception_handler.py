from fastapi import Request, HTTPException
from fastapi.exceptions import RequestValidationError
from starlette.responses import JSONResponse

from app.utils.response_utils import ResponseUtils


async def http_exception_handler(request: Request, exc: HTTPException):
    # 可以根据status_code来进行不同的自定义响应
    if exc.status_code == 401:
        return JSONResponse(
            status_code=401,
            content=ResponseUtils.unauthorized(message=exc.detail)
        )
    return JSONResponse(
        status_code=exc.status_code,
        content=ResponseUtils.failed(
            message=exc.detail
        )
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=422,
        content=ResponseUtils.validation_error(
            validation_errors=exc.errors()
        )
    )


async def global_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content=ResponseUtils.server_error()
    )
