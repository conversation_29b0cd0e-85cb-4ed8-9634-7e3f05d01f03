# 基于Celery的任务分发架构（超级无敌巨无霸帅架构以及使用说明书）

## 项目概述

从直接数据库操作的FastAPI改造为基于Celery+Redis的任务分发架构。参考唐主任之前的interface+remote_celery_worker。一共有两个项目，一个是yongzhou_manger，这是前端负责转发请求到redis中，我已经做好的任务发布的封装，特别容易上手。一个是yongzhou_woker,这个是后端内网内真正负责处理业务的项目，从redis中取任务执行并返回，最后前端再把结果返回给客户

# 以下说明中前端指的是yongzhou_managment项目,后端指的是yongzhou_worker项目

## 架构说明

```
客户端请求 -> FastAPI接口 -> Celery任务发布 -> Redis队列 -> 消费者服务 -> 数据库
```

## 前端项目结构

```
app/
├── celery_app.py          # Celery应用配置
├── tasks/                 # 任务定义模块
│   ├── user_tasks.py      # 用户相关任务
│   ├── role_tasks.py      # 角色相关任务
│   └── permission_tasks.py # 权限相关任务
├── controller/            # 控制器层
├── service/              # 服务层（已改造为任务发布）
├── router/               # 路由层
└── settings/             # 配置文件（已添加Celery配置）
```

## 后端项目结构

```
yongzhou_worker/
├── celery_app.py          # Celery应用配置
├── config.py              # 配置文件
├── worker.py              # Worker启动脚本
├── init_database.py       # 数据库初始化脚本
├── requirements.txt       # 依赖包列表
├── database/              # 数据库模块
│   ├── __init__.py
│   ├── mysql_client.py    # MySQL客户端
│   └── redis_client.py    # Redis客户端
├── business/              # 业务逻辑模块
│   ├── __init__.py
│   └── user_business.py   # 用户业务逻辑
├──settings/
│   ├── config.py
│   └── 一系列的配置文件
└── tasks/                 # 任务模块
    ├── __init__.py
    ├── proxy.py           # 代理任务（核心）
    ├── user_tasks.py      # 用户任务
    ├── role_tasks.py      # 角色任务
    └── permission_tasks.py # 权限任务
```

## 前端分发任务核心逻辑

```python

"""
代理任务模块 - 参考interface项目设计
统一的任务代理，用于将操作转发给内网worker处理
"""
from celery_task.celery_config import celery_app


@celery_app.task(name="yongzhou.proxy.execute")
def execute(operation, args=None, kwargs=None):
    """
    统一的代理任务，用于执行各种业务操作
    
    Args:
        operation: 操作路径，如 'user.login', 'user.register' 等
        args: 位置参数列表
        kwargs: 关键字参数字典
        
    Returns:
        任务执行结果（由内网worker处理）
    """
    # 这里只是占位实现，实际的业务逻辑由内网worker执行
    # worker会根据operation参数来决定执行哪个具体的业务函数
    pass
```

```python
class TaskPublisher:
    """任务发布器"""

    @classmethod
    def publish_task(cls, operation: str, args: Optional[List] = None, kwargs: Optional[Dict] = None,
                    wait_for_result: bool = True, timeout: int = 30) -> Dict[str, Any]:
        """
        发布任务到Celery队列

        Args:
            operation: 操作路径，如 'user.login', 'user.register' 等
            args: 位置参数列表
            kwargs: 关键字参数字典
            wait_for_result: 是否等待任务执行完成
            timeout: 等待超时时间（秒）

        Returns:
            Dict: 任务执行结果或状态信息
        """
        if args is None:
            args = []
        if kwargs is None:
            kwargs = {}

        # 使用apply_async调用代理任务
        result = execute.apply_async(kwargs={
            'operation': operation,
            'args': args,
            'kwargs': kwargs
        })

        if wait_for_result:
            try:
                # 等待任务执行完成并获取结果
                task_result = result.get(timeout=timeout)
                return task_result
            except Exception as e:
                return {
                    'success': False,
                    'message': f'任务执行超时或失败: {str(e)}',
                    'task_id': result.id
                }
        else:
            # 不等待结果，直接返回任务信息
            return {
                'task_id': result.id,
                'status': 'pending',
                'message': f'{operation}请求已提交，正在处理中'
            }
    
    @classmethod
    def publish_user_task(cls, operation: str, *args, wait_for_result: bool = True, timeout: int = 30, **kwargs) -> Dict[str, Any]:
        """发布用户相关任务"""
        return cls.publish_task(f'user.{operation}', list(args), kwargs, wait_for_result, timeout)

    @classmethod
    def publish_role_task(cls, operation: str, *args, wait_for_result: bool = True, timeout: int = 30, **kwargs) -> Dict[str, Any]:
        """发布角色相关任务"""
        return cls.publish_task(f'role.{operation}', list(args), kwargs, wait_for_result, timeout)

    @classmethod
    def publish_permission_task(cls, operation: str, *args, wait_for_result: bool = True, timeout: int = 30, **kwargs) -> Dict[str, Any]:
        """发布权限相关任务"""
        return cls.publish_task(f'permission.{operation}', list(args), kwargs, wait_for_result, timeout)
```

这是前端分发任务的核心，所有任务都会走这个函数，具体区分用operation字段指定 '
模块名.方法名',还有参数等，我使用这种方法的话，就实现高度封装了，你就不需要再去celery_app文件中再去注册任务了，至于说会暴露后端真正的项目结果，我想的办法是在后端做了一个映射（参考下面）

```python
# 操作映射表 - 将operation路径映射到具体的业务函数
OPERATION_MAP = {
    # 用户相关操作
    'user.login': user_business.login,
    'user.register': user_business.register,
    'user.get_roles': user_business.get_user_roles,
    'user.get_permissions': user_business.get_user_permissions,
    'user.change_roles': user_business.change_user_roles,
    'user.check_permission': user_business.check_permission,

}
```

## 前端具体的设计思路与举例

首先在router目录下创建一个子路由，并且将子路由配置到init中

```python
# 将所有的子路由整合在一起，最后在主应用中注册
all_routers = [user_router, task_router]

# 在子路由中配置接口路径
router_map=[
    {"path":"/login","endpoint":login,"methods":["POST"]},
    {"path":"/register","endpoint":register,"methods":["POST"]},
    {"path":"/roles","endpoint":get_roles,"methods":["GET"]},
    {"path":"/permissions","endpoint":get_permissions,"methods":["GET"]},
    {"path":"/change_roles","endpoint":change_user_roles,"methods":["POST"]},
    {"path":"/check_permission","endpoint":check_permission1,"methods":["GET"]},
    {"path":"/test1","endpoint":test1,"methods":["GET"]}
]
```

在controller层写最外层的接口函数，直接调用service层的方法

```python

def test1(content:str):
    print(1)
    result1 = test1_service(content)
    return result1
```

login_service方法在service中，你要发布任务的话直接调用TaskPublisher.publish_user_task，参数第一个是你controller层的方法名，第二个是参数列表。service写完就可以到后端去写真正的业务逻辑了

```python
def test1_service(content:str):
    return TaskPublisher.publish_user_task('test1', content,
                                           wait_for_result=True, timeout=30)
```

## 后端实现举例

来到后端项目中，首先在business文件夹下面创建自己的业务文件，如user_business.py，并写上对应的业务方法

```python
    @staticmethod
    def test1_business(content:str):
        print(content)
        return  {'success': True, 'message': 'test1成功'}
```

接着在tasks/proxy.py文件中进行映射，其中键是你前端传递的operation参数，值是你写在business文件夹中业务方法的名字（一定要一一对应）。如果你已经完成到了这一步，恭喜你已经顺利完成了接口编写的全部流程。接下来直接测试即可

```python
# 操作映射表 - 将operation路径映射到具体的业务函数
OPERATION_MAP = {
    # 用户相关操作
    'user.login': user_business.login,
    'user.register': user_business.register,
    'user.get_roles': user_business.get_user_roles,
    'user.get_permissions': user_business.get_user_permissions,
    'user.change_roles': user_business.change_user_roles,
    'user.check_permission': user_business.check_permission,
    'user.test1':user_business.test1_business,
}
```

补充1：你还可以在task目录下新建模块任务，比如说定时任务，当然要记得在celery中进行注册和配置，具体参考项目remote_celery_worker

```python
"""
user_task.py
用户相关任务 - 如果需要独立的用户任务可以在这里定义
目前主要通过proxy.py的代理模式处理
"""
from celery_app import app

# 这里可以定义一些特殊的用户任务，如果需要的话
# 大部分用户操作通过proxy.py处理
```

补充2：数据库的相关操作有两种方式，第一种是使用orm操作，在models文件夹下创建对应的实体类，另一种是使用封装好的数据库操作方法，具体参考后端项目中的database/README.md文件和database/MIGRATION.md文件

## 接口测试方法

首先启动前端的main函数，启动fastapi，然后打开前端项目中的celery_worker.py文件，从里面复制下面命令到终端启动celery

```
celery -A celery_task.celery worker --loglevel=info --pool=solo
```

接下来到后端项目中，直接启动worker，如果遇到问题就同样使用下面命令在终端中启动celery

```
celery -A celery_task.celery worker --loglevel=info --pool=solo
```

