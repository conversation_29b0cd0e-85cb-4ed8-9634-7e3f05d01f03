"""
任务相关路由
"""
from fastapi import APIRouter

from app.controller.task_controller import get_task_status, get_task_result, cancel_task
from app.utils.router_helper import RouterHelper

# 创建任务路由
task_router = APIRouter(prefix="/task", tags=["任务管理"])

# 路由映射
router_map = [
    {"path": "/status/{task_id}", "endpoint": get_task_status, "methods": ["GET"]},
    {"path": "/result/{task_id}", "endpoint": get_task_result, "methods": ["GET"]},
    {"path": "/cancel/{task_id}", "endpoint": cancel_task, "methods": ["POST"]},
]

# 注册路由
router_helper = RouterHelper(task_router)
router_helper.register_routes(router_map)
