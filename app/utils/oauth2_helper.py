from datetime import datetime, timedelta
from typing import Optional, List

from fastapi import HTT<PERSON>Ex<PERSON>, Depends
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from jose import jwt
from pydantic import BaseModel
from starlette import status
from starlette.requests import Request

from app.models import User
from app.settings.config import settings


# 设计一个类，产生的实体保存到token中加密传输
class TokenData(BaseModel):
    user_id: int
    username: str
    permissions: Optional[List[str]] = None
    exp: Optional[datetime] = None
    type: Optional[str] = None


class JWTTokenManger(object):
    """
      专门用于生成token，刷新token，验证token的工具类
    """

    def __init__(self):
        self.secret_key = settings.jwt.secret_key
        self.algorithm = settings.jwt.algorithm
        self.access_token_expire_minutes = settings.jwt.access_token_expire_minutes
        self.refresh_token_expire_days = settings.jwt.refresh_token_expire_days

    # 生成token的方法
    def create_access_token(self, user: User) -> str:
        # 获取用户权限
        permissions = User.get_user_permission(user.id)
        permission_names = [perm.name for perm in permissions] if permissions else None
        # 创建token
        token_data = {
            "user_id": user.id,
            "username": user.username,
            "permissions": permission_names,
            "exp": datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes),
            "type": "access_token"
        }
        return jwt.encode(token_data, self.secret_key, algorithm=self.algorithm)

    # 刷新token的方法
    def create_refresh_token(self, user: User) -> str:
        # 创建token
        token_data = {
            "user_id": user.id,
            "username": user.username,
            "exp": datetime.utcnow() + timedelta(days=self.refresh_token_expire_days),
            "type": "refresh_token"
        }
        return jwt.encode(token_data, self.secret_key, algorithm=self.algorithm)

    # 验证token的方法
    def verify_token(self, token: str) -> TokenData:
        # 验证令牌为了防止token是篡改的，try来捕获异常
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return TokenData(**payload)
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="token已过期")
        except jwt.JWTError:
            raise HTTPException(status_code=401, detail="token无效")

    # 使用刷新令牌获取访问令牌，当访问令牌失效了，但是刷新令牌依然有效
    def refresh_access_token(self, refresh_token: str) -> str:
        try:
            payload = jwt.decode(refresh_token, self.secret_key, algorithms=[self.algorithm])
            if payload.get("type") != "refresh_token":
                raise HTTPException(
                    status_code=401,
                    detail="无效的刷新令牌"
                )
            user_id = payload.get("user_id")
            user = User.find_user_by_id(user_id)
            if not user:
                raise HTTPException(
                    status_code=401,
                    detail="用户不存在"
                )
            return self.create_access_token(user)

        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="token已过期")
        except jwt.JWTError:
            raise HTTPException(status_code=401, detail="token无效")


class GlobalAuthMiddleware:
    """
        创建全局中间件，验证请求地址是否在白名单，验证请求request中是否有携带token（顺便验证一下格式）
    """

    def __init__(self):
        self.jwt_manager = JWTTokenManger()
        self.whitelist_paths = settings.jwt.whitelist_paths

    # 判断是否在白名单里面
    def is_whitelisted(self, path: str) -> bool:
        if path in self.whitelist_paths:
            return True
        # 检查前缀是否匹配
        for white_path in self.whitelist_paths:
            if white_path.endswith("*"):
                prefix = white_path[:-1]
                if path.startswith(prefix):
                    return True
        return False

    # 验证request中请求头里面是否携带token
    async def verify_token_from_request(self, request: Request) -> Optional[User]:
        # 检查是否在白名单里面
        if self.is_whitelisted(request.url.path):
            return None
        # 获取Authorization请求头{headers：{"Authorization":"Bearer token"}}
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            raise HTTPException(
                status_code=401,
                detail="请求头中没有携带token",
                headers={"WWW-Authenticate": "Bearer"}
            )

        # 验证Bearer格式
        if not auth_header.startswith("Bearer"):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization请求缺少Bearer",
                headers={"WWW-Authenticate": "Bearer"}
            )

        # 提取token
        token = auth_header[7:]  # 去掉前缀“Bearer ”
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization请求中token为空",
                headers={"WWW-Authenticate": "Bearer"}
            )

        # 验证token
        token_data = self.jwt_manager.verify_token(token)
        if token_data.type != "access_token":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的访问令牌",
                headers={"WWW-Authenticate": "Bearer"}
            )
        # 获取用户信息
        user = User.find_user_by_id(token_data.user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户不存在"
            )
        return user


class PermissionChecker:
    @staticmethod
    def check_permission(required_permission: str):
        # 检查用户是否拥有指定权限装饰器
        def permission_dependency(current_user: User = Depends(get_current_user)):
            if not User.has_permission(current_user.id, required_permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足,需要权限{required_permission}"
                )
            return current_user

        return permission_dependency

    @staticmethod
    def check_permissions(required_permission: List[str]):
        # 检查用户是否拥有指定权限装饰器
        def permission_dependency(current_user: User = Depends(get_current_user)):
            for permission in required_permission:
                if User.has_permission(current_user.id, permission):
                    return current_user
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足,需要权限{required_permission}"
            )

        return permission_dependency

    @staticmethod
    def check_all_permissions(required_permission: List[str]):
        def permission_dependency(current_user: User = Depends(get_current_user)):
            for permission in required_permission:
                if User.has_permission(current_user.id, permission):
                    return current_user
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足,需要权限{required_permission}"
            )

        return permission_dependency


# 获取当前用户
def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> User:
    token = credentials.credentials
    token_data = jwt_manager.verify_token(token)
    if token_data.type != "access_token":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )
    user = User.find_user_by_id(token_data.user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在"
        )
    return user


# 检查权限装饰器函数
def require_permission(permission: str):
    return permission_checker.check_permission(permission)


def require_permissions(required_permission: List[str]):
    return permission_checker.check_all_permissions(required_permission)


def require_all_permissions(required_permission: List[str]):
    return permission_checker.check_all_permissions(required_permission)


# 实例化工具,外部可以直接调用
jwt_manager = JWTTokenManger()
global_auth_middleware = GlobalAuthMiddleware()
permission_checker = PermissionChecker()
