from select import select
from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import relationship

from app.models import Base, Session


class Role(Base):
    __tablename__ = 'role'
    id = Column(Integer, primary_key=True, autoincrement=True, comment='角色编号')
    name = Column(String(50), comment='角色名称')
    description = Column(String(100), comment='角色描述')
    # 多对多的配置，角色和权限的关联
    permissions = relationship("Permission", secondary="role_permission", back_populates="roles", lazy="joined")
    # 多对多 用户和角色
    users = relationship("User", secondary="user_role", back_populates="roles", lazy="joined")

    def __repr__(self):
        # 获取类名
        class_name = self.__class__.__name__
        # 获取所有列属性
        columns = self.__class__.__table__.columns.keys()
        # 构建字段表示
        attrs = [f"{col}={getattr(self, col, None)!r}" for col in columns]
        return f"<{class_name}({', '.join(attrs)})>"

    """
        根据条件来查询对应角色
        **kwargs = name = "超级管理员"
        get_one(name="超级管理员",description="adsadsa")
    """

    @classmethod
    def get_one(cls, **kwargs):
        with Session() as session:
            stmt = select(cls).filter_by(**kwargs)
            result = session.execute(stmt).scalars().all()
            return result

    """
        查询角色对应的权限
        [{id:1,name:"超级管理员",permissions:[{
            id:1,name:"用户管理",description:"用户管理"
        }]}]
    """

    @classmethod
    def get_role_permissions(cls, role_id: int):
        with Session() as session:
            role = session.get(cls, role_id)
            if role is not None:
                return role.permissions
            return []

    """
        查询角色对应的所有用户
    """

    @classmethod
    def get_role_users(cls, role_id: int):
        with Session() as session:
            role = session.get(cls, role_id)
            if role is not None:
                return role.users
            return []

    """
        根据权限的名字来查询角色的名字
    """

    @classmethod
    def get_role_by_permission_name(cls, permission_name: str):
        from app.models.permission.permission import Permission
        with Session() as session:
            stmt = select(cls).join(Permission).where(Permission.name == permission_name)
            roles = session.execute(stmt).scalars().all()
            return roles

    """
        查询拥有多个权限的角色，基本上每个角色都有多个权限
    """

    @classmethod
    def get_roles_by_permission_length(cls, permission_names: list):
        from app.models.permission.permission import Permission
        with Session() as session:
            stmt = select(cls).join(Permission).where(Permission.name.in_(permission_names))
            roles = session.execute(stmt).scalars().unique().all()
            return roles

    """
        查询所有角色及其权限,自定义数据结构
    """

    @classmethod
    def get_all_roles_with_permissions(cls):
        with Session() as session:
            roles = session.execute(
                select(cls)
            ).scalars().unique().all()
            return [
                {
                    "id": role.id,
                    "name": role.name,
                    # "permissions":[
                    #     {
                    #         "id":permission.id,
                    #         "name":permission.name,
                    #         "description":permission.description
                    #     } for permission in role.permissions
                    # ]
                    "permissions": [
                        perm.name for perm in role.permissions
                    ]
                } for role in roles
            ]

    """
        为角色分配权限
    """

    @classmethod
    def assign_permissions(cls, role_id: int, permissions_ids: list):
        from app.models.permission.permission import Permission
        with Session() as session:
            role = session.get(cls, role_id)
            if role is not None:
                permissions = session.execute(
                    select(Permission).where(Permission.id.in_(permissions_ids))
                ).scalars().unique().all()
                role.permissions = permissions
                session.commit()
                return True
            return False

    """
        移除角色对应的权限
    """

    @classmethod
    def remove_permissions(cls, role_id: int, permissions_ids: list):
        with Session() as session:
            role = session.get(cls, role_id)
            if role is not None:
                # 要删除的权限列表
                permissions_to_remove = [
                    perm for perm in role.permissions if perm.id in permissions_ids
                ]
                # 循环找到角色里面的权限，匹配成功就删除
                for perm in permissions_to_remove:
                    role.permissions.remove(perm)
                session.commit()
                return True
            return False

    """
        检查角色是否拥有指定的权限
    """

    @classmethod
    def has_permissions(cls, role_id: int, permission_name: str):
        with Session() as session:
            role = session.get(cls, role_id)
            if role is not None:
                return any(perm.name == permission_name for perm in role.permissions)
            return False
