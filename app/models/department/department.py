from sqlalchemy import Column, Integer, String, select
from sqlalchemy.orm import relationship

from app.models import Base, Session


class Department(Base):
    __tablename__ = 'department'
    id = Column(Integer, primary_key=True, autoincrement=True, comment='自增主键')
    dept_name = Column(String(20), comment='部门名称')
    dept_description = Column(String(100), comment='部门描述')
    # 部门下面有多个用户
    # back_populates 代表要实现双向关联，指明和另一个模型的关系
    users = relationship("User",back_populates="department")

    def __repr__(self):
        # 获取类名
        class_name = self.__class__.__name__
        # 获取所有列属性
        columns = self.__class__.__table__.columns.keys()
        # 构建字段表示
        attrs = [f"{col}={getattr(self, col, None)!r}" for col in columns]
        return f"<{class_name}({', '.join(attrs)})>"

    @classmethod
    def find_dept_with_users(cls):
        from app.models.user.user import User
        with Session() as session:
            stmt = select(Department).join(User,User.dept_id == Department.id)
            dept = session.execute(stmt).scalars().first()
            print(f"dept:{dept}, users:{dept.users}")




