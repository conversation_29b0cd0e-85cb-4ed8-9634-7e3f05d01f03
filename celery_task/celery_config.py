"""
Celery应用配置 - 参考interface项目的简洁设计
用于创建和配置Celery实例，作为任务分发的核心
"""
import base64
from datetime import timedelta

from Crypto.Cipher import AES
from celery import Celery

# 创建Celery应用实例
celery_app = Celery('celery_task')

# 公网的redis
redis = "jrUvMlYyXfs1HzGZH62H3WmU0wkBGf4BlBWc6T3jnSoNSSfzTV2GQFvhHf4nAb7S"
key_m = "tRVZzkJ00CfldcvlgWMjs/7uNyEHjO62eq+bS/X1L3Y="
K = 'hnyzzxyy0salmon0'


# 内网的redis


def decrypt(key, enc):  # 解密
    decipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
    dec = decipher.decrypt(base64.b64decode(enc))
    return dec.strip(b'\0').decode('utf-8')


privacy = decrypt(decrypt(K, key_m), redis)
# 配置Celery - 参考interface项目的配置方式
config = {
    'broker_url': privacy + '/2',
    'result_backend': privacy + '/3',
    'task_serializer': 'json',
    'result_serializer': 'json',
    'accept_content': ['json'],
    'timezone': 'Asia/Shanghai',
    'enable_utc': True,

    # 性能优化配置
    'worker_prefetch_multiplier': 1,
    'task_acks_late': True,
    'worker_max_tasks_per_child': 100,
    'worker_max_memory_per_child': 1024 * 1024 * 100,
    'task_queue_max_priority': 10,
    'task_default_priority': 5,

    # 错误处理配置
    'task_acks_on_failure_or_timeout': False,
    'task_reject_on_worker_lost': True,
    'result_backend_always_retry': True,
    'result_backend_max_retries': 10,
    'result_backend_thread_safe': True,
    'result_expires': timedelta(hours=1),

    # 连接重试配置
    'broker_connection_retry_on_startup': True,

    # 包含代理任务模块
    'include': ['app.tasks.proxy']
}

celery_app.config_from_object(config)
