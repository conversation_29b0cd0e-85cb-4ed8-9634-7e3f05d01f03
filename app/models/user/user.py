from sqlalchemy import Column, Integer, String, select, ForeignKey
from sqlalchemy.dialects.mysql import insert
from sqlalchemy.orm import relationship

from app.models import Department
from app.models.base_model import Base, Session
from app.schemas.user_schema import UserEntity

"""
 表示层：代表接受请求获取数据，调用业务函数来处理业务，返回结果给前端
 业务层：比如权限的操作，加密的操作，第三方API的对接
 持久层： 只负责喝数据库进行交互，它里面设计的
"""


class User(Base):
    # 定义好当前模型在数据库里面的表名
    __tablename__ = 'user'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='这是用户唯一的编号')
    username = Column(String(100), unique=True, nullable=False, comment='用户名字段')
    password = Column(String(100), comment='密码（加密存储）')
    email = Column(String(100), comment='电子邮箱')
    full_name = Column(String(50), comment='用户全称')
    dept_id = Column(Integer, ForeignKey("department.id"), comment='部门编号')
    # 关联映射的时候，采用字符串的方式来关联‘Wechat’
    # relationship建立的是对象之间的关联，你可以通过User获取微信对象信息
    # --userlist返回的结果是否用列表来存储
    # --backref自动在关联的模型上面，反向生成一个属性，Wechat上面生成user属性
    wechat = relationship("Wechat", uselist=False, backref="user")
    department = relationship("Department", back_populates="users")

    # 多对多的关联，用户和角色
    roles = relationship("Role", secondary="user_role", back_populates="users", lazy="joined")

    #  repr用于代码调试的时候，格式化输出模型对象，类似于java中的tostring
    def __repr__(self):
        # 获取类名
        class_name = self.__class__.__name__
        # 获取所有列属性
        columns = self.__class__.__table__.columns.keys()
        # 构建字段表示
        attrs = [f"{col}={getattr(self, col, None)!r}" for col in columns]
        return f"<{class_name}({', '.join(attrs)})>"

    @classmethod
    def add_user(cls, user: UserEntity):
        with Session() as session:
            # username=user.username,password=user.password
            stmt = insert(User).values(**user.model_dump())
            result = session.execute(stmt)
            session.commit()
            return result.rowcount  # 增删改都可以返回rowcount反应处理结果

    @classmethod
    def find_user_by_username(cls, username: str):
        with Session() as session:
            stmt = select(User).where(User.username == username)
            result = session.execute(stmt).scalars().first()  # scalars封装成对象，直接查是结果集，first是获取第一个结果
            return result

    @classmethod
    def find_user_by_id(cls, id: int):
        from app.models.wechat.wechat import Wechat
        with Session() as session:
            stmt = select(User).outerjoin(Wechat, User.id == Wechat.user_id).where(User.id == id)
            user = session.execute(stmt).scalars().first()
            print(user)
            # 关联查询默认开启了懒加载，如果你不用wechat不会查询封装
            print(user.wechat)
            return user

    @classmethod
    def find_user_with_department(cls):
        with Session() as session:
            stmt = select(User).join(Department, User.dept_id == Department.id)
            user = session.execute(stmt).scalars().first()
            print(f"user.username:{user.username},dept_name:{user.department.dept_name}")
            return user

    @classmethod
    def find_user_by_name(cls, username: str):
        with Session() as session:
            stmt = select(User).where(User.username == username)
            user = session.execute(stmt).scalars().all()
            print(f"user:{user}")

    @classmethod
    def find_user_limit(cls):
        with Session() as session:
            # stmt = select(User).order_by(User.id)
            stmt = select(User).limit(1)
            users = session.execute(stmt).scalars().all()
            print(f"users:{users}")

    @classmethod
    def find_user_and_dept(cls):
        with Session() as session:
            stmt = select(User).outerjoin(Department, User.dept_id == Department.id)
            users = session.execute(stmt).scalars().all()
            print(f"users:{users}")

    """
      根据用户查询所有的角色
    """

    @classmethod
    def get_user_roles(cls, user_id: int):
        with Session() as session:
            user = session.get(cls, user_id)
            if user is not None:
                return user.roles
            return []

    """
        根据用户查询所有的权限
    """

    @classmethod
    def get_user_permission(cls, user_id: int):
        with Session() as session:
            user = session.get(cls, user_id)
            if user is not None:
                roles = user.roles
                permissions = set()
                for role in roles:
                    permissions.update(role.permissions)
                return permissions
            return []

    """
        为用户分配角色
    """
    @classmethod
    def assign_role(cls, user_id: int, role_ids: list):
        from app.models.role.role import Role
        with Session() as session:
            user = session.get(cls, user_id)
            if user is not None:
                roles = session.execute(
                    # 将 unique() 从 Select 移至 scalars() 之后
                    select(Role).where(Role.id.in_(role_ids))
                ).scalars().unique().all()  # 在结果集上调用 unique() 去重
                print(f"roles:{roles}")
                user.roles = roles
                session.commit()
                return True
            return False

    """
        检查用户是否拥有某个权限
    """

    @classmethod
    def has_permission(cls, user_id: int, permission_name: str):
        with Session() as session:
            user = session.get(cls, user_id)
            if user:
                # 遍历用户所有角色的权限，检查是否存在目标权限
                for role in user.roles:
                    for permission in role.permissions:
                        if permission.name == permission_name:
                            return True
        return False
