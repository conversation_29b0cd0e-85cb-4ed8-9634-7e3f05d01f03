"""
测试Celery任务发布功能 - 等待结果版本
"""
import time
from app.utils.task_publisher import TaskPublisher
from app.celery_app import celery_app


def test_task_publishing_with_result():
    """测试任务发布功能 - 等待结果"""
    print("开始测试Celery任务发布（等待结果）...")

    # 测试登录任务 - 等待结果
    print("\n1. 测试登录任务发布（等待结果）:")
    try:
        login_result = TaskPublisher.publish_user_task('login', "test_user", "test_password", wait_for_result=True, timeout=30)
        print(f"登录结果: {login_result}")
    except Exception as e:
        print(f"登录任务执行失败: {e}")

    # 测试注册任务 - 等待结果
    print("\n2. 测试注册任务发布（等待结果）:")
    try:
        register_result = TaskPublisher.publish_user_task('register', "new_user", "new_password", wait_for_result=True, timeout=30, email="<EMAIL>")
        print(f"注册结果: {register_result}")
    except Exception as e:
        print(f"注册任务执行失败: {e}")

    print("\n任务发布测试完成！")


def test_task_publishing_async():
    """测试任务发布功能 - 异步模式"""
    print("\n开始测试Celery任务发布（异步模式）...")

    # 测试登录任务 - 不等待结果
    print("\n1. 测试登录任务发布（异步）:")
    login_result = TaskPublisher.publish_user_task('login', "test_user", "test_password", wait_for_result=False)
    print(f"登录任务ID: {login_result.get('task_id')}")
    print(f"任务状态: {login_result.get('status')}")
    print(f"任务消息: {login_result.get('message')}")

    return login_result.get('task_id')


def test_task_status_query(task_id):
    """测试任务状态查询"""
    print(f"\n测试任务状态查询 - 任务ID: {task_id}")
    
    try:
        result = celery_app.AsyncResult(task_id)
        print(f"任务状态: {result.state}")
        print(f"任务信息: {result.info}")
        
        if result.ready():
            print(f"任务已完成，结果: {result.result}")
        else:
            print("任务尚未完成")
            
    except Exception as e:
        print(f"查询任务状态时出错: {e}")


if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 等待结果模式（需要worker运行）")
    print("2. 异步模式（不等待结果）")

    choice = input("请选择 (1/2): ").strip()

    if choice == "1":
        # 测试等待结果模式
        test_task_publishing_with_result()
    elif choice == "2":
        # 测试异步模式
        task_id = test_task_publishing_async()

        # 测试任务状态查询
        if task_id:
            test_task_status_query(task_id)
    else:
        print("无效选择")

    print(f"\n可以使用以下命令启动worker来处理任务:")
    print(f"cd yongzhou_worker")
    print(f"python worker.py")
    print(f"\n或者使用:")
    print(f"celery -A yongzhou_worker.celery_app worker --loglevel=info")
