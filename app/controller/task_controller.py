"""
任务状态查询控制器
用于查询Celery任务的执行状态和结果
"""
from app.utils.response_utils import ResponseUtils
from celery_task.celery_config import celery_app


def get_task_status(task_id: str):
    """
    获取任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        Dict: 任务状态信息
    """
    try:
        # 获取任务结果
        result = celery_app.AsyncResult(task_id)

        if result.state == 'PENDING':
            # 任务等待中
            response = {
                'task_id': task_id,
                'state': result.state,
                'status': 'pending',
                'message': '任务正在等待处理'
            }
        elif result.state == 'PROGRESS':
            # 任务进行中
            response = {
                'task_id': task_id,
                'state': result.state,
                'status': 'in_progress',
                'message': '任务正在处理中',
                'progress': result.info.get('progress', 0) if result.info else 0
            }
        elif result.state == 'SUCCESS':
            # 任务成功
            response = {
                'task_id': task_id,
                'state': result.state,
                'status': 'success',
                'message': '任务执行成功',
                'result': result.result
            }
        elif result.state == 'FAILURE':
            # 任务失败
            response = {
                'task_id': task_id,
                'state': result.state,
                'status': 'failure',
                'message': '任务执行失败',
                'error': str(result.info)
            }
        else:
            # 其他状态
            response = {
                'task_id': task_id,
                'state': result.state,
                'status': 'unknown',
                'message': f'未知任务状态: {result.state}'
            }

        return ResponseUtils.success("查询成功", data=response)

    except Exception as e:
        return ResponseUtils.failed(f"查询任务状态失败: {str(e)}")


def get_task_result(task_id: str):
    """
    获取任务结果
    
    Args:
        task_id: 任务ID
        
    Returns:
        Dict: 任务结果
    """
    try:
        result = celery_app.AsyncResult(task_id)

        if result.ready():
            if result.successful():
                return ResponseUtils.success("获取结果成功", data=result.result)
            else:
                return ResponseUtils.failed(f"任务执行失败: {str(result.info)}")
        else:
            return ResponseUtils.success("任务尚未完成", data={
                'task_id': task_id,
                'state': result.state,
                'message': '任务尚未完成，请稍后查询'
            })

    except Exception as e:
        return ResponseUtils.failed(f"获取任务结果失败: {str(e)}")


def cancel_task(task_id: str):
    """
    取消任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        Dict: 取消结果
    """
    try:
        celery_app.control.revoke(task_id, terminate=True)
        return ResponseUtils.success("任务取消成功", data={'task_id': task_id})
    except Exception as e:
        return ResponseUtils.failed(f"取消任务失败: {str(e)}")
