from typing import Dict, Any

from app.schemas.user_schema import UserEntity
from app.utils.task_publisher import TaskPublisher

"""
 description: 登录业务 - 使用代理任务发布
"""


def login_service(user: UserEntity) -> Dict[str, Any]:
    # 使用TaskPublisher发布登录任务并等待结果
    return TaskPublisher.publish_user_task('login', user.username, user.password,
                                           wait_for_result=True, timeout=30)


def register_service(user: UserEntity) -> Dict[str, Any]:
    # 使用TaskPublisher发布注册任务并等待结果
    return TaskPublisher.publish_user_task(
        'register',
        user.username,
        user.password,
        wait_for_result=True,
        timeout=30,
        email=getattr(user, 'email', None)
    )


def find_user_roles(user_id: int) -> Dict[str, Any]:
    # 使用TaskPublisher发布获取用户角色任务并等待结果
    return TaskPublisher.publish_user_task('get_roles', user_id, wait_for_result=True, timeout=30)


def find_user_permission(user_id: int) -> Dict[str, Any]:
    # 使用TaskPublisher发布获取用户权限任务并等待结果
    return TaskPublisher.publish_user_task('get_permissions', user_id, wait_for_result=True,
                                           timeout=30)


def add_role(user_id: int, role_ids: list) -> Dict[str, Any]:
    # 使用TaskPublisher发布修改用户角色任务并等待结果
    return TaskPublisher.publish_user_task('change_roles', user_id, role_ids, wait_for_result=True,
                                           timeout=30)


def check_permission(user_id: int, permission_name: str) -> Dict[str, Any]:
    # 使用TaskPublisher发布检查用户权限任务并等待结果
    return TaskPublisher.publish_user_task('check_permission', user_id, permission_name,
                                           wait_for_result=True, timeout=30)
