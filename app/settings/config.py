
"""
 用于设计一个类，读取我们指定的yml文件，后续其他模块中要使用配置，导入config就可以以获取，全局配置文件
 pyyaml用于解析yml文件，让项目能够支持yml类配置，后续还可以正确读取文件
 pydantic要进行类型的校验
"""
from pathlib import Path

import yaml
from pydantic import BaseModel


class JWTConfig(BaseModel):
    secret_key: str
    algorithm: str
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    # 白名单路径，这些路径不需要token验证
    whitelist_paths: list[str] = []


class MysqlConfig(BaseModel):
    host: str
    port: int  # 改为 int 类型
    db: str
    user: str
    password: str
    url: str


class RedisConfig(BaseModel):
    host: str
    port: int  # 改为 int 类型
    db: int  # 改为 int 类型，因为 Redis 数据库编号是整数
    password: str
    url: str


class CeleryConfig(BaseModel):
    broker_url: str
    result_backend: str
    task_serializer: str = 'json'
    accept_content: list[str] = ['json']
    result_serializer: str = 'json'
    timezone: str = 'Asia/Shanghai'
    enable_utc: bool = True


class Settings(BaseModel):
    app_env: str
    debug: bool
    static_dir: str
    template_dir: str
    mysql: MysqlConfig
    redis: RedisConfig
    jwt: JWTConfig  # 添加 jwt 配置字段
    celery: CeleryConfig  # 添加 celery 配置字段


def load_yaml(path):
    with open(path, encoding='utf-8') as f:  # 添加 encoding='utf-8' 参数
        return yaml.safe_load(f)


def load_settings():
    base_path = Path(__file__).parent
    env_conf = load_yaml(base_path / 'config_env.yml')
    env = env_conf['current_env']
    static_dir = env_conf['static_dir']
    template_dir = env_conf['template_dir']
    config = load_yaml(base_path / f'config_{env}.yml')
    return Settings(
        app_env=config['app_env'],
        debug=config['debug'],
        static_dir=static_dir,
        template_dir=template_dir,
        mysql=MysqlConfig(**config['mysql']),  # **表示把对象的所有属性传递过去
        redis=RedisConfig(**config['redis']),
        # 新增的jwt配置
        jwt=JWTConfig(**config['jwt']),
        # 新增的celery配置
        celery=CeleryConfig(**config['celery']),
    )


settings = load_settings()
