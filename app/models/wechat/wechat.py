from sqlalchemy import Column, Integer, String, ForeignKey, select
from sqlalchemy.orm import joinedload

from app.models import Base, Session


class Wechat(Base):
    __tablename__ = 'wechat'
    id = Column(Integer, primary_key=True, autoincrement=True, comment='自增主键')
    openid = Column(String(100), unique=True, nullable=False, comment='用户openid')
    # 设置外键约束，创建表的时候，wechat表多一个字段 user_id
    user_id = Column(Integer, ForeignKey("user.id"), unique=True, comment='用户id')

    #  repr用于代码调试的时候，格式化输出模型对象，类似于java中的tostring
    def __repr__(self):
        # 获取类名
        class_name = self.__class__.__name__
        # 获取所有列属性
        columns = self.__class__.__table__.columns.keys()
        # 构建字段表示
        attrs = [f"{col}={getattr(self, col, None)!r}" for col in columns]
        return f"<{class_name}({', '.join(attrs)})>"

    @classmethod
    def get_by_id(cls, id: int):
        from app.models.user.user import User
        with Session() as session:
            # 使用 joinedload 预加载 user 关联对象，避免懒加载问题
            # stmt = select(Wechat).options(joinedload(Wechat.user)).where(Wechat.id == id)
            stmt = select(Wechat).join(User).where(User.id == Wechat.user_id)
            result = session.execute(stmt).scalars().first()
            print(result.user)
            return result



