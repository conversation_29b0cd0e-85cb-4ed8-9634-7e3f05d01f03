from datetime import datetime

from sqlalchemy import Column, DateTime, func, String, Integer, create_engine, select, update, delete
from sqlalchemy.dialects.mysql import insert
from sqlalchemy.orm import declared_attr, declarative_base, sessionmaker, scoped_session



"""
 如果你的数据库表已经创建了，再次执行的时候就不会再创建了
 create_engine创建一个数据库的链接需要传入URL地址
 sessionmaker用于创建session的工厂，涉及到以后有很多个连接对象
 scoped_session创建出的session是线程安全的，并发环境下保证数据的安全
 create_all自动建表，只要继承了Base类的子类，执行函数都会自动给我们创建表
 代码解释：engine = create_engine(DATABASE_URL,echo=True,future=True)
 echo输出SQL日志，方便我们进行代码调试，future启动SQLALCHEMY2.0兼容模式（SQLALCHEMY升级到2.0版本，在API的操作上和1.0版本有区别）
 2.0版本可以向下兼容，在2.0版本中使用1.0版本的API来完成数据库操作）
 建议在生成环境下面，echo设置False
"""

"""
  目前默认只有一个user模型，所以只创建了一个表，后续我们把模型都设计好了，你可以一键完成所有表的创建
"""

"""
 declarative_base生成所有模型的积累，所有模型集成这个基类
 tablename指定我们模型的表名，如果不指定也可以生成表，名字默认是user
 username，password都是用户的字段，映射到数据库的时候，需要提供对应字段约束
 repr用于代码调试的时候，格式化输出模型对象
"""




class BaseModel:
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()

    create_at=Column(DateTime,default=func.now(),comment="创建时间")
    update_at=Column(DateTime,default=func.now(),onupdate=func.now(),comment="更新时间")


#创建Base对象，作为其他模型依赖的基础
Base = declarative_base(cls=BaseModel)

class User(Base):
    # 定义好当前模型在数据库里面的表名
    __tablename__ = 'user'

    id = Column(Integer,primary_key=True,autoincrement=True,comment='这是用户唯一的编号')
    username = Column(String(100),unique=True,nullable=False,comment='用户名字段')
    password = Column(String(100),comment='密码（加密存储）')
    email = Column(String(100),comment='电子邮箱')
    full_name = Column(String(50),comment='用户全称')

    #  repr用于代码调试的时候，格式化输出模型对象
    def __repr__(self):
        return f"<User(id={self.id},username={self.username})>"

# 我们选择的驱动pymysql
DATABASE_URL= 'mysql+pymysql://root:root@127.0.0.1:3306/rbac_system'
#创建数据库引擎
engine = create_engine(DATABASE_URL,echo=True,future=True)
#创建Session工厂
SessionLocal = sessionmaker(autocommit=False,autoflush=False,bind=engine)
#创建Session对象,线程安全的Session
Session = scoped_session(SessionLocal)
# 创建所有的表
def init_db():
    Base.metadata.create_all(bind=engine)

def create_user(username,password,email,full_name):
    with Session() as session:
        #user = User(username=username,password=password,email=email,full_name=full_name)
        stmt= insert(User).values(username=username,password=password,email=email,full_name=full_name)
        result= session.execute(stmt)
        session.commit()
        user_id=result.inserted_primary_key[0]
        print(f"用户{username}创建成功，用户编号是{user_id}")

def get_user_by_id(id:int):
    with Session() as session:
        result = session.get(User,id)
        print( result)

def get_all_user():
    with Session() as session:
        stmt=select(User)
        result = session.execute(stmt)
        all_user = result.scalars().all()
        print(f"查询结果：{all_user}")

def update_user(user_id:int,**keyargs):
    with Session() as session:
        stmt=update(User).where(User.id==user_id).values(**keyargs)
        result=session.execute(stmt)
        session.commit()
        print(f"修改结果：{result.rowcount}")

def del_user(user_id:int):
    with Session() as session:
        stmt=delete(User).where(User.id==user_id)
        result = session.execute(stmt)
        session.commit()
        print(f"删除结果：{result.rowcount}")

if __name__ == '__main__':
    # init_db()
    # print('数据库表已经创建完毕')
    create_user('user1','123456','<EMAIL>','用户1')
    # get_user_by_id(1)
    # get_all_user()
    # update_user(1,username='user1')
    #del_user(1)




