from sqlalchemy import Table, Column, Foreign<PERSON>ey, Integer

from app.models import Base

# 用户和角色的中间表
user_role = Table(
    "user_role",
    Base.metadata,
    Column("user_id", Integer, ForeignKey("user.id")),
    Column("role_id", Integer, Foreign<PERSON>ey("role.id")),
)

# 角色和权限的中间表
role_permission = Table(
    "role_permission",
    Base.metadata,
    Column("role_id", Integer, ForeignKey("role.id")),
    Column("permission_id", Integer, ForeignKey("permission.id")),
)

