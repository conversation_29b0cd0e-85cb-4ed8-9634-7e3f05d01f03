from datetime import datetime

from sqlalchemy import Column, DateTime, func, String, Integer, create_engine, select, update, delete
from sqlalchemy.dialects.mysql import insert
from sqlalchemy.orm import declared_attr, declarative_base, sessionmaker, scoped_session

class BaseModel:
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()

    create_at=Column(DateTime,default=func.now(),comment="创建时间")
    update_at=Column(DateTime,default=func.now(),onupdate=func.now(),comment="更新时间")


#创建Base对象，作为其他模型依赖的基础
Base = declarative_base(cls=BaseModel)

# 我们选择的驱动pymysql
DATABASE_URL= 'mysql+pymysql://root:root@127.0.0.1:3306/rbac_system'
#创建数据库引擎
engine = create_engine(DATABASE_URL,echo=True,future=True)
#创建Session工厂
SessionLocal = sessionmaker(autocommit=False,autoflush=False,bind=engine)
#创建Session对象,线程安全的Session
Session = scoped_session(SessionLocal)
# 创建所有的表
def init_db():
    Base.metadata.create_all(bind=engine)






