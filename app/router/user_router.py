from fastapi import APIRouter

from app.controller.user_controller import login, register, get_roles, get_permissions, change_user_roles, \
    check_permission1, test1
from app.utils.router_helper import RouterHelper

# APIRouter作用：模块化管理路由，你可以把不同的业务分配到不同的文件中（router）
# 最后统一注册到主应用中。很方便进行团队协作开发。APIRouter创建的路由称为子路由
# router_map：path代表请求地址，endpoint代表控制器的函数，以后只要访问这个地址，直接调用对应控制器函数
# methods:定义请求方式，add_api_route:他是APIRouter的一个方法，用于手动注册路由，好处就是可以批量添加路由映射
user_router = APIRouter(prefix="/user",tags=["用户管理"])

# map中定义字典数据的时候，默认只提供了三个key，还可以增加其他的key
router_map=[
    {"path":"/login","endpoint":login,"methods":["POST"]},
    {"path":"/register","endpoint":register,"methods":["POST"]},
    {"path":"/roles","endpoint":get_roles,"methods":["GET"]},
    {"path":"/permissions","endpoint":get_permissions,"methods":["GET"]},
    {"path":"/change_roles","endpoint":change_user_roles,"methods":["POST"]},
    {"path":"/check_permission","endpoint":check_permission1,"methods":["GET"]},
    {"path":"/test1","endpoint":test1,"methods":["GET"]}
]

# for route in router_map:
#     user_router.add_api_route(
#         route["path"],
#         route["endpoint"],
#         methods=route["methods"]
#     )

router_helper= RouterHelper(user_router)
router_helper.register_routes(router_map)