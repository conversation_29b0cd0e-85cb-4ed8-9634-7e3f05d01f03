import bcrypt


def hash_password(plain_password: str) -> str:
    """
    对密码进行哈希加密返回加密后的字符串
    :param plain_password: 传递的明文密码
    :return: 加密后的字符串
    """
    hashed = bcrypt.hashpw(plain_password.encode("utf-8"), bcrypt.gensalt())
    return hashed.decode("utf-8")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码是否匹配
    :param plain_password: 传递的明文密码
    :param hashed_password: 数据库中的哈希密码
    :return: 匹配返回True,不匹配返回False
    """
    return bcrypt.checkpw(plain_password.encode("utf-8"), hashed_password.encode("utf-8"))
