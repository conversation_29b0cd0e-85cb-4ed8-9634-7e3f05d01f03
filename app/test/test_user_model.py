from sqlalchemy import insert

from app.models import Session, User, init_db, Wechat,Department

user_model = User()
wechat_model = Wechat()
dept_model = Department()

def test_login(username:str):
    result = user_model.find_user_by_username(username)
    print(result)

def test_find_user_by_id(id:int):
    result = user_model.find_user_by_id(id)
    print(result)

def test_find_wechat():
    result = wechat_model.get_by_id(1)
    #print(result.user)
    print(result)

if __name__ == '__main__':
    # test_add_user("xiaowang","123456","<EMAIL>","小王")
    # test_login("xiaowang")
    init_db()
    # test_find_user_by_id(3)
    # test_find_wechat()
    # user_model.find_user_with_department()
    # dept_model.find_dept_with_users()
    # user_model.find_user_by_name("xiaowang1")
    # user_model.find_user_limit()
    # user_model.find_user_and_dept()